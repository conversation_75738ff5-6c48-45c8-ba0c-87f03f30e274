#!/usr/bin/env python3
"""
删除指定的活跃订单
"""

import sqlite3
import os
import sys
from datetime import datetime

def delete_orders():
    """删除指定的订单"""
    
    # 要删除的订单ID
    order_ids = ['1749392266488', 'EDJ202050603001']
    
    # 数据库路径
    db_path = 'app.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 删除前查询订单信息 ===")
        for order_id in order_ids:
            cursor.execute('''
                SELECT edaijia_order_id, customer_name, edj_status_code,
                       pickup_address, destination_address, created_at
                FROM "order"
                WHERE edaijia_order_id = ?
            ''', (order_id,))
            
            order = cursor.fetchone()
            if order:
                print(f"找到订单: {order_id}")
                print(f"  客户: {order[1]}")
                print(f"  状态: {order[2]}")
                print(f"  起点: {order[3]}")
                print(f"  终点: {order[4]}")
                print(f"  创建时间: {order[5]}")
                print()
            else:
                print(f"未找到订单: {order_id}")
        
        print("=== 开始删除订单 ===")
        total_deleted = 0
        
        for order_id in order_ids:
            cursor.execute('DELETE FROM orders WHERE edaijia_order_id = ?', (order_id,))
            deleted_count = cursor.rowcount
            total_deleted += deleted_count
            print(f"订单 {order_id}: 删除了 {deleted_count} 条记录")
        
        # 提交更改
        conn.commit()
        print(f"\n✅ 总共删除了 {total_deleted} 条订单记录")
        
        print("\n=== 删除后验证 ===")
        for order_id in order_ids:
            cursor.execute('SELECT * FROM orders WHERE edaijia_order_id = ?', (order_id,))
            order = cursor.fetchone()
            if order:
                print(f"⚠️  警告: 订单 {order_id} 仍然存在")
            else:
                print(f"✅ 确认: 订单 {order_id} 已成功删除")
        
        # 查询剩余的活跃订单
        print("\n=== 剩余活跃订单统计 ===")
        cursor.execute('''
            SELECT edaijia_order_id, customer_name, edj_status_code, 
                   pickup_address, destination_address, created_at
            FROM orders 
            WHERE edj_status_code IN ('102', '180', '301', '302', '303')
            ORDER BY created_at DESC
        ''')
        
        active_orders = cursor.fetchall()
        
        if active_orders:
            print(f"剩余活跃订单数量: {len(active_orders)}")
            for order in active_orders:
                print(f"  - {order[0]} | {order[1]} | 状态:{order[2]} | {order[5]}")
        else:
            print("✅ 没有剩余的活跃订单")
        
        # 查询所有订单统计
        print("\n=== 订单总体统计 ===")
        cursor.execute('SELECT COUNT(*) FROM orders')
        total_orders = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM orders WHERE edj_status_code = "304"')
        completed_orders = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM orders WHERE edj_status_code IN ("403", "404")')
        cancelled_orders = cursor.fetchone()[0]
        
        print(f"总订单数: {total_orders}")
        print(f"已完成订单: {completed_orders}")
        print(f"已取消订单: {cancelled_orders}")
        print(f"活跃订单: {len(active_orders)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 删除订单时发生错误: {e}")
        return False

if __name__ == "__main__":
    print("MyProj2025 - 删除活跃订单工具")
    print("=" * 50)
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = delete_orders()
    
    if success:
        print("\n🎉 订单删除操作成功完成!")
    else:
        print("\n❌ 订单删除操作失败!")
        sys.exit(1)
