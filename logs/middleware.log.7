2025-06-08 22:25:15,584 INFO: 原始店面名称参数: '龙华店' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4191]
2025-06-08 22:25:15,588 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:25:15,590 INFO: 解码后店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4210]
2025-06-08 22:25:15,595 INFO: 店面 龙华店 活跃订单列表: 3 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4242]
2025-06-08 22:25:23,690 INFO: 查询取消费用: EDJ20250608004, 操作人: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2469]
2025-06-08 22:25:23,716 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:191]
2025-06-08 22:25:23,772 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-08 16:25:23.772592+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:88]
2025-06-08 22:25:23,799 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:226]
2025-06-08 22:25:23,801 INFO: 调用e代驾查询取消费API: http://localhost:5001/order/getCancelFee [in /home/<USER>/MyProj2025/app/api/routes.py:2412]
2025-06-08 22:25:23,818 ERROR: 查询取消费失败: 订单不存在 [in /home/<USER>/MyProj2025/app/api/routes.py:2442]
2025-06-08 22:25:27,290 INFO: 开始取消订单: EDJ20250608004, 操作人: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2265]
2025-06-08 22:25:27,299 INFO: 调用e代驾查询取消费API: http://localhost:5001/order/getCancelFee [in /home/<USER>/MyProj2025/app/api/routes.py:2412]
2025-06-08 22:25:27,312 ERROR: 查询取消费失败: 订单不存在 [in /home/<USER>/MyProj2025/app/api/routes.py:2442]
2025-06-08 22:26:48,383 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:26:48,389 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:26:48,396 INFO: 店面 龙华店 活跃订单数量: 3 [in /home/<USER>/MyProj2025/app/api/routes.py:3880]
2025-06-08 22:26:48,406 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3936]
2025-06-08 22:26:49,833 INFO: 原始店面名称参数: '龙华店' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4191]
2025-06-08 22:26:49,835 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:26:49,835 INFO: 解码后店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4210]
2025-06-08 22:26:49,840 INFO: 店面 龙华店 活跃订单列表: 3 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4242]
2025-06-08 22:32:49,248 INFO: 原始店面名称参数: 'é¾ååº' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4191]
2025-06-08 22:32:49,250 INFO: 店面名称解码: 'é¾ååº' -> 'é¾ååº' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:32:49,251 INFO: 解码后店面名称: 'é¾ååº' [in /home/<USER>/MyProj2025/app/api/routes.py:4210]
2025-06-08 22:32:49,256 INFO: 店面 é¾ååº 活跃订单列表: 0 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4242]
2025-06-08 22:34:59,613 INFO: 原始店面名称参数: 'é¾ååº' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4191]
2025-06-08 22:34:59,615 INFO: 店面名称解码: 'é¾ååº' -> 'é¾ååº' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:34:59,616 INFO: 解码后店面名称: 'é¾ååº' [in /home/<USER>/MyProj2025/app/api/routes.py:4210]
2025-06-08 22:34:59,619 INFO: 店面 é¾ååº 活跃订单列表: 0 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4242]
2025-06-08 22:41:51,926 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 22:43:22,203 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 22:44:52,668 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 22:45:22,526 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 22:47:22,265 INFO: 查询取消费用: EDJ20250608004, 操作人: 周鑫芸 [in /home/<USER>/MyProj2025/app/api/routes.py:2469]
2025-06-08 22:47:22,289 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:191]
2025-06-08 22:47:22,309 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-08 16:47:22.309313+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:88]
2025-06-08 22:47:22,321 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:226]
2025-06-08 22:47:22,322 INFO: 调用e代驾查询取消费API: http://localhost:5001/order/getCancelFee [in /home/<USER>/MyProj2025/app/api/routes.py:2412]
2025-06-08 22:47:22,333 INFO: 查询取消费成功: EDJ20250608004, 费用: ¥5.0 [in /home/<USER>/MyProj2025/app/api/routes.py:2437]
2025-06-08 22:47:22,334 INFO: 查询取消费用成功: EDJ20250608004, 费用: ¥5.0 [in /home/<USER>/MyProj2025/app/api/routes.py:2498]
2025-06-09 09:43:50,413 INFO: 全部门店 唯一客户统计: 总订单 28, 唯一客户 13 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-09 09:43:51,199 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 2, 总订单: 20, 总金额: 2422.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4144]
2025-06-09 09:43:53,414 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:43:53,442 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:43:53,444 INFO: 图表统计API - 解码后的店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4054]
2025-06-09 09:43:53,446 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:43:53,464 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 2, 总订单: 5, 总金额: 650.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4144]
2025-06-09 09:43:53,580 INFO: 店面 龙华店 唯一客户统计: 总订单 8, 唯一客户 6 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-09 09:43:53,581 INFO: 仪表盘统计 - 店面: 龙华店, 客户: 6, 订单: 8, 待处理: 3, 进行中: 0, 已完成: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:4641]
2025-06-09 09:44:00,062 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:44:00,063 INFO: 图表统计API - 解码后的店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4054]
2025-06-09 09:44:00,078 INFO: 图表统计 - 店面: 龙华店, 时间范围: day, 数据点: 3, 总订单: 4, 总金额: 451.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4144]
2025-06-09 09:44:04,911 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:44:04,912 INFO: 图表统计API - 解码后的店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4054]
2025-06-09 09:44:04,921 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 2, 总订单: 5, 总金额: 650.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4144]
2025-06-09 09:44:07,246 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:44:07,247 INFO: 图表统计API - 解码后的店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4054]
2025-06-09 09:44:07,257 INFO: 图表统计 - 店面: 龙华店, 时间范围: year, 数据点: 1, 总订单: 5, 总金额: 650.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4144]
2025-06-09 09:44:16,304 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:44:16,306 INFO: 图表统计API - 解码后的店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4054]
2025-06-09 09:44:16,312 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 2, 总订单: 5, 总金额: 650.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4144]
2025-06-09 09:45:18,221 INFO: 店面名称解码: '总部' -> '总部' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:45:18,227 INFO: 店面名称解码: '总部' -> '总部' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:45:18,272 INFO: 店面 总部 历史订单数量: 0 [in /home/<USER>/MyProj2025/app/api/routes.py:3936]
2025-06-09 09:45:18,275 INFO: 店面 总部 活跃订单数量: 0 [in /home/<USER>/MyProj2025/app/api/routes.py:3880]
2025-06-09 09:45:32,136 INFO: 店面名称解码: '万国城店' -> '万国城店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:45:32,140 INFO: 店面名称解码: '万国城店' -> '万国城店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:45:32,150 INFO: 店面 万国城店 历史订单数量: 11 [in /home/<USER>/MyProj2025/app/api/routes.py:3936]
2025-06-09 09:45:32,152 INFO: 店面 万国城店 活跃订单数量: 1 [in /home/<USER>/MyProj2025/app/api/routes.py:3880]
2025-06-09 09:46:04,338 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:46:04,349 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:46:04,351 INFO: 店面 龙华店 活跃订单数量: 3 [in /home/<USER>/MyProj2025/app/api/routes.py:3880]
2025-06-09 09:46:04,363 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3936]
2025-06-09 09:46:05,363 INFO: 原始店面名称参数: '龙华店' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4191]
2025-06-09 09:46:05,366 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
