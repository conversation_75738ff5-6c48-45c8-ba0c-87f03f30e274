2025-06-08 22:07:59,837 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 22:07:59,844 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 22:07:59,846 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-08 22:07:59,847 INFO: 取车地址地理编码成功: (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2867]
2025-06-08 22:07:59,848 INFO: 送车地址缺少坐标，尝试地理编码: 深圳市南山区深圳湾 [in /home/<USER>/MyProj2025/app/api/routes.py:2879]
2025-06-08 22:07:59,848 INFO: 🌐 百度地图API调用: 深圳市南山区深圳湾, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E5%8D%97%E5%B1%B1%E5%8C%BA%E6%B7%B1%E5%9C%B3%E6%B9%BE&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 22:08:00,126 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 22:08:00,127 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.95469212699223, 'lat': 22.5283998968019}, 'precise': 1, 'confidence': 80, 'comprehension': 100, 'level': '餐饮'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 22:08:00,128 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 22:08:00,129 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 22:08:00,129 INFO: ✅ 百度地图API地址置信度良好: 深圳市南山区深圳湾, confidence=80, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 22:08:00,130 INFO: ✅ 百度地图API成功解析: 深圳市南山区深圳湾 -> (113.95469212699223, 22.5283998968019) [confidence=80] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 22:08:00,133 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市南山区深圳湾 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 22:08:00,135 INFO: 地址解析成功: 深圳市南山区深圳湾 -> (113.95469212699223, 22.5283998968019) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-08 22:08:00,136 INFO: 送车地址地理编码成功: (113.95469212699223, 22.5283998968019) [in /home/<USER>/MyProj2025/app/api/routes.py:2883]
2025-06-08 22:08:00,137 INFO: Store name exact match: '龙华店' from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:566]
2025-06-08 22:08:00,138 INFO: 店面认证成功: 店长周鑫芸 -> 店面龙华店 (匹配方式: 完全匹配) [in /home/<USER>/MyProj2025/app/api/routes.py:2955]
2025-06-08 22:08:00,139 INFO: 开始重复订单检查: 手机号=13800138000, 车牌=粤B12345, 店面=龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3351]
2025-06-08 22:08:00,149 INFO: 重复订单检查通过: 手机号=13800138000, 车牌=粤B12345 [in /home/<USER>/MyProj2025/app/api/routes.py:3582]
2025-06-08 22:08:00,150 INFO: Authentication attempt: manager='周鑫芸', ip=127.0.0.1, user_agent=python-requests/2.31.0 [in /home/<USER>/MyProj2025/app/api/routes.py:695]
2025-06-08 22:08:00,154 INFO: SECURITY: Manager '周鑫芸' authenticated successfully for store '龙华店' (ID: 1) from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:743]
2025-06-08 22:08:00,155 INFO: Creating order for store 龙华店 by manager 周鑫芸 [in /home/<USER>/MyProj2025/app/api/routes.py:3023]
2025-06-08 22:08:00,157 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:191]
2025-06-08 22:08:00,168 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-08 16:08:00.168575+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:88]
2025-06-08 22:08:00,178 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:226]
2025-06-08 22:08:00,193 ERROR: Error creating plugin order: 'status' is an invalid keyword argument for Order [in /home/<USER>/MyProj2025/app/api/routes.py:3162]
Traceback (most recent call last):
  File "/home/<USER>/MyProj2025/app/api/routes.py", line 3093, in f6_submit_order
    order = Order(
            ^^^^^^
  File "<string>", line 4, in __init__
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/orm/state.py", line 564, in _initialize_instance
    with util.safe_reraise():
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/orm/state.py", line 562, in _initialize_instance
    manager.original_init(*mixed[1:], **kwargs)
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/orm/decl_base.py", line 2139, in _declarative_constructor
    raise TypeError(
TypeError: 'status' is an invalid keyword argument for Order
2025-06-08 22:09:29,967 INFO: 取车地址缺少坐标，尝试地理编码: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2863]
2025-06-08 22:09:29,968 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 22:09:30,171 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 22:09:30,172 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 22:09:30,172 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 22:09:30,172 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 22:09:30,173 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 22:09:30,173 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 22:09:30,176 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 22:09:30,177 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-08 22:09:30,177 INFO: 取车地址地理编码成功: (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2867]
2025-06-08 22:09:30,178 INFO: 送车地址缺少坐标，尝试地理编码: 深圳市南山区深圳湾 [in /home/<USER>/MyProj2025/app/api/routes.py:2879]
2025-06-08 22:09:30,178 INFO: 🌐 百度地图API调用: 深圳市南山区深圳湾, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E5%8D%97%E5%B1%B1%E5%8C%BA%E6%B7%B1%E5%9C%B3%E6%B9%BE&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 22:09:30,424 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 22:09:30,425 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.95469212699223, 'lat': 22.5283998968019}, 'precise': 1, 'confidence': 80, 'comprehension': 100, 'level': '餐饮'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 22:09:30,426 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 22:09:30,426 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 22:09:30,427 INFO: ✅ 百度地图API地址置信度良好: 深圳市南山区深圳湾, confidence=80, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 22:09:30,428 INFO: ✅ 百度地图API成功解析: 深圳市南山区深圳湾 -> (113.95469212699223, 22.5283998968019) [confidence=80] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 22:09:30,434 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市南山区深圳湾 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 22:09:30,436 INFO: 地址解析成功: 深圳市南山区深圳湾 -> (113.95469212699223, 22.5283998968019) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-08 22:09:30,436 INFO: 送车地址地理编码成功: (113.95469212699223, 22.5283998968019) [in /home/<USER>/MyProj2025/app/api/routes.py:2883]
2025-06-08 22:09:30,437 INFO: Store name exact match: '龙华店' from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:566]
2025-06-08 22:09:30,437 INFO: 店面认证成功: 店长周鑫芸 -> 店面龙华店 (匹配方式: 完全匹配) [in /home/<USER>/MyProj2025/app/api/routes.py:2955]
2025-06-08 22:09:30,438 INFO: 开始重复订单检查: 手机号=13800138000, 车牌=粤B12345, 店面=龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3351]
2025-06-08 22:09:30,447 INFO: 重复订单检查通过: 手机号=13800138000, 车牌=粤B12345 [in /home/<USER>/MyProj2025/app/api/routes.py:3582]
2025-06-08 22:09:30,448 INFO: Authentication attempt: manager='周鑫芸', ip=127.0.0.1, user_agent=curl/8.5.0 [in /home/<USER>/MyProj2025/app/api/routes.py:695]
