2025-06-09 13:42:44,218 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-09 13:42:44,231 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-09 13:42:44,232 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-09 13:42:44,237 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-09 13:42:44,241 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-09 13:42:44,243 INFO: 🌐 百度地图API调用: 深圳市-福田区-福中一路2001号, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82-%E7%A6%8F%E7%94%B0%E5%8C%BA-%E7%A6%8F%E4%B8%AD%E4%B8%80%E8%B7%AF2001%E5%8F%B7&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-09 13:42:44,556 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-09 13:42:44,557 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06384478032084, 'lat': 22.55179375946674}, 'precise': 1, 'confidence': 80, 'comprehension': 100, 'level': '门址'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-09 13:42:44,557 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-09 13:42:44,558 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-09 13:42:44,558 INFO: ✅ 百度地图API地址置信度良好: 深圳市-福田区-福中一路2001号, confidence=80, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-09 13:42:44,559 INFO: ✅ 百度地图API成功解析: 深圳市-福田区-福中一路2001号 -> (114.06384478032084, 22.55179375946674) [confidence=80] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-09 13:42:44,565 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市-福田区-福中一路2001号 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-09 13:42:44,568 INFO: 地址解析成功: 深圳市-福田区-福中一路2001号 -> (114.06384478032084, 22.55179375946674) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-09 13:42:44,573 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:191]
2025-06-09 13:42:44,586 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-09 07:42:44.586628+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:88]
2025-06-09 13:42:44,601 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:226]
2025-06-09 13:42:44,602 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2153]
2025-06-09 13:42:44,615 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> 深圳市-福田区-福中一路2001号, 费用: ¥63.6 [in /home/<USER>/MyProj2025/app/api/routes.py:2226]
2025-06-09 13:43:05,405 INFO: 调用百度Place API: query=深圳市, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4447]
2025-06-09 13:43:05,969 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4485]
2025-06-09 13:43:07,127 INFO: 调用百度Place API: query=深圳市erton, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4447]
2025-06-09 13:43:07,587 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4485]
2025-06-09 13:43:07,660 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-09 13:43:07,878 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-09 13:43:07,879 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-09 13:43:07,879 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-09 13:43:07,880 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-09 13:43:07,881 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-09 13:43:07,881 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-09 13:43:07,887 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-09 13:43:07,889 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-09 13:43:07,890 INFO: 🌐 百度地图API调用: 深圳市erton, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82erton&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-09 13:43:08,189 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-09 13:43:08,189 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06455183658751, 'lat': 22.548456637984177}, 'precise': 0, 'confidence': 20, 'comprehension': 57, 'level': '城市'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-09 13:43:08,190 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-09 13:43:08,190 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-09 13:43:08,191 WARNING: ⚠️ 百度地图API地址置信度较低但可接受: 深圳市erton, confidence=20, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:114]
2025-06-09 13:43:08,191 INFO: ✅ 百度地图API成功解析: 深圳市erton -> (114.06455183658751, 22.548456637984177) [confidence=20] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-09 13:43:08,195 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市erton [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-09 13:43:08,196 INFO: 地址解析成功: 深圳市erton -> (114.06455183658751, 22.548456637984177) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-09 13:43:08,200 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:191]
2025-06-09 13:43:08,212 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-09 07:43:08.212577+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:88]
2025-06-09 13:43:08,223 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:226]
2025-06-09 13:43:08,224 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2153]
2025-06-09 13:43:08,236 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> 深圳市erton, 费用: ¥65.9 [in /home/<USER>/MyProj2025/app/api/routes.py:2226]
2025-06-09 13:43:30,667 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-09 13:43:30,968 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-09 13:43:30,968 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-09 13:43:30,969 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-09 13:43:30,970 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-09 13:43:30,970 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-09 13:43:30,971 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-09 13:43:30,976 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-09 13:43:30,978 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
