2025-06-09 09:50:16,517 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:50:16,520 INFO: 解码后店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4210]
2025-06-09 09:50:16,526 INFO: 店面 龙华店 活跃订单列表: 3 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4242]
2025-06-09 09:50:25,219 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:50:25,222 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:50:25,228 INFO: 店面 龙华店 活跃订单数量: 3 [in /home/<USER>/MyProj2025/app/api/routes.py:3880]
2025-06-09 09:50:25,235 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3936]
2025-06-09 09:50:26,990 INFO: 原始店面名称参数: '龙华店' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4191]
2025-06-09 09:50:26,993 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:50:26,994 INFO: 解码后店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4210]
2025-06-09 09:50:27,000 INFO: 店面 龙华店 活跃订单列表: 3 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4242]
2025-06-09 09:51:47,849 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:51:47,859 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:51:47,862 INFO: 店面 龙华店 活跃订单数量: 3 [in /home/<USER>/MyProj2025/app/api/routes.py:3880]
2025-06-09 09:51:47,872 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3936]
2025-06-09 09:51:48,641 INFO: 原始店面名称参数: '龙华店' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4191]
2025-06-09 09:51:48,643 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 09:51:48,643 INFO: 解码后店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4210]
2025-06-09 09:51:48,647 INFO: 店面 龙华店 活跃订单列表: 3 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4242]
2025-06-09 09:51:50,789 INFO: 查询取消费用: EDJ202506083001, 操作人: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2469]
2025-06-09 09:51:50,798 INFO: 调用e代驾查询取消费API: http://localhost:5001/order/getCancelFee [in /home/<USER>/MyProj2025/app/api/routes.py:2412]
2025-06-09 09:51:50,812 ERROR: 查询取消费失败: 订单不存在 [in /home/<USER>/MyProj2025/app/api/routes.py:2442]
2025-06-09 09:51:53,474 INFO: 开始取消订单: EDJ202506083001, 操作人: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2265]
2025-06-09 09:51:53,481 INFO: 调用e代驾查询取消费API: http://localhost:5001/order/getCancelFee [in /home/<USER>/MyProj2025/app/api/routes.py:2412]
2025-06-09 09:51:53,497 ERROR: 查询取消费失败: 订单不存在 [in /home/<USER>/MyProj2025/app/api/routes.py:2442]
2025-06-09 13:40:12,515 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-09 13:40:14,612 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-09 13:40:20,365 INFO: 全部门店 唯一客户统计: 总订单 28, 唯一客户 13 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-09 13:40:23,051 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 2, 总订单: 20, 总金额: 2422.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4144]
2025-06-09 13:40:26,806 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 13:40:26,842 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 13:40:26,844 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 13:40:26,845 INFO: 图表统计API - 解码后的店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4054]
2025-06-09 13:40:26,916 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 2, 总订单: 5, 总金额: 650.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4144]
2025-06-09 13:40:26,963 INFO: 店面 龙华店 唯一客户统计: 总订单 8, 唯一客户 6 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-09 13:40:26,965 INFO: 仪表盘统计 - 店面: 龙华店, 客户: 6, 订单: 8, 待处理: 3, 进行中: 0, 已完成: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:4641]
2025-06-09 13:42:33,825 INFO: 调用百度Place API: query=sh, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4447]
2025-06-09 13:42:34,342 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4485]
2025-06-09 13:42:37,748 INFO: 调用百度Place API: query=shenzhenshi, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4447]
2025-06-09 13:42:38,262 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4485]
2025-06-09 13:42:38,285 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-09 13:42:38,746 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-09 13:42:38,747 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-09 13:42:38,748 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-09 13:42:38,748 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-09 13:42:38,749 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-09 13:42:38,749 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-09 13:42:38,754 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-09 13:42:38,756 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-09 13:42:38,757 INFO: 🌐 百度地图API调用: shenzhenshi, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhenshi&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-09 13:42:39,015 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-09 13:42:39,016 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06455183658751, 'lat': 22.548456637984177}, 'precise': 0, 'confidence': 20, 'comprehension': 57, 'level': '城市'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-09 13:42:39,016 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-09 13:42:39,017 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-09 13:42:39,018 WARNING: ⚠️ 百度地图API地址置信度较低但可接受: shenzhenshi, confidence=20, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:114]
2025-06-09 13:42:39,018 INFO: ✅ 百度地图API成功解析: shenzhenshi -> (114.06455183658751, 22.548456637984177) [confidence=20] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-09 13:42:39,024 INFO: 地址解析成功 - 提供商: baidu, 地址: shenzhenshi [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-09 13:42:39,026 INFO: 地址解析成功: shenzhenshi -> (114.06455183658751, 22.548456637984177) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-09 13:42:39,031 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:191]
2025-06-09 13:42:39,055 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-09 07:42:39.055026+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:88]
2025-06-09 13:42:39,073 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:226]
2025-06-09 13:42:39,075 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2153]
2025-06-09 13:42:39,089 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> shenzhenshi, 费用: ¥65.9 [in /home/<USER>/MyProj2025/app/api/routes.py:2226]
2025-06-09 13:42:43,930 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-09 13:42:44,215 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-09 13:42:44,217 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-09 13:42:44,217 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
