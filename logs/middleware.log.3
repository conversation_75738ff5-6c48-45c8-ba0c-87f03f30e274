2025-06-09 13:43:30,979 INFO: 🌐 百度地图API调用: 深圳市-福田区-景田路78号深圳妇儿大厦-裙楼1层儿童大堂, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82-%E7%A6%8F%E7%94%B0%E5%8C%BA-%E6%99%AF%E7%94%B0%E8%B7%AF78%E5%8F%B7%E6%B7%B1%E5%9C%B3%E5%A6%87%E5%84%BF%E5%A4%A7%E5%8E%A6-%E8%A3%99%E6%A5%BC1%E5%B1%82%E5%84%BF%E7%AB%A5%E5%A4%A7%E5%A0%82&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-09 13:43:31,268 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-09 13:43:31,269 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.05110213700492, 'lat': 22.55854009961723}, 'precise': 1, 'confidence': 75, 'comprehension': 18, 'level': '商务大厦'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-09 13:43:31,270 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-09 13:43:31,270 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-09 13:43:31,271 INFO: ✅ 百度地图API地址置信度良好: 深圳市-福田区-景田路78号深圳妇儿大厦-裙楼1层儿童大堂, confidence=75, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-09 13:43:31,272 INFO: ✅ 百度地图API成功解析: 深圳市-福田区-景田路78号深圳妇儿大厦-裙楼1层儿童大堂 -> (114.05110213700492, 22.55854009961723) [confidence=75] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-09 13:43:31,277 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市-福田区-景田路78号深圳妇儿大厦-裙楼1层儿童大堂 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-09 13:43:31,279 INFO: 地址解析成功: 深圳市-福田区-景田路78号深圳妇儿大厦-裙楼1层儿童大堂 -> (114.05110213700492, 22.55854009961723) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-09 13:43:31,282 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:191]
2025-06-09 13:43:31,294 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-09 07:43:31.294123+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:88]
2025-06-09 13:43:31,308 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:226]
2025-06-09 13:43:31,309 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2153]
2025-06-09 13:43:31,323 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> 深圳市-福田区-景田路78号深圳妇儿大厦-裙楼1层儿童大堂, 费用: ¥57.5 [in /home/<USER>/MyProj2025/app/api/routes.py:2226]
2025-06-09 13:43:41,488 INFO: 取车地址缺少坐标，尝试地理编码: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2863]
2025-06-09 13:43:41,489 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-09 13:43:41,749 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-09 13:43:41,750 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-09 13:43:41,750 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-09 13:43:41,751 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-09 13:43:41,751 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-09 13:43:41,752 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-09 13:43:41,757 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-09 13:43:41,759 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-09 13:43:41,760 INFO: 取车地址地理编码成功: (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2867]
2025-06-09 13:43:41,761 INFO: 送车地址缺少坐标，尝试地理编码: 深圳市-福田区-景田路78号深圳妇儿大厦-裙楼1层儿童大堂 [in /home/<USER>/MyProj2025/app/api/routes.py:2879]
2025-06-09 13:43:41,761 INFO: 🌐 百度地图API调用: 深圳市-福田区-景田路78号深圳妇儿大厦-裙楼1层儿童大堂, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82-%E7%A6%8F%E7%94%B0%E5%8C%BA-%E6%99%AF%E7%94%B0%E8%B7%AF78%E5%8F%B7%E6%B7%B1%E5%9C%B3%E5%A6%87%E5%84%BF%E5%A4%A7%E5%8E%A6-%E8%A3%99%E6%A5%BC1%E5%B1%82%E5%84%BF%E7%AB%A5%E5%A4%A7%E5%A0%82&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-09 13:43:41,948 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-09 13:43:41,949 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.05110213700492, 'lat': 22.55854009961723}, 'precise': 1, 'confidence': 75, 'comprehension': 18, 'level': '商务大厦'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-09 13:43:41,950 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-09 13:43:41,950 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-09 13:43:41,951 INFO: ✅ 百度地图API地址置信度良好: 深圳市-福田区-景田路78号深圳妇儿大厦-裙楼1层儿童大堂, confidence=75, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-09 13:43:41,951 INFO: ✅ 百度地图API成功解析: 深圳市-福田区-景田路78号深圳妇儿大厦-裙楼1层儿童大堂 -> (114.05110213700492, 22.55854009961723) [confidence=75] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-09 13:43:41,955 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市-福田区-景田路78号深圳妇儿大厦-裙楼1层儿童大堂 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-09 13:43:41,956 INFO: 地址解析成功: 深圳市-福田区-景田路78号深圳妇儿大厦-裙楼1层儿童大堂 -> (114.05110213700492, 22.55854009961723) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-09 13:43:41,957 INFO: 送车地址地理编码成功: (114.05110213700492, 22.55854009961723) [in /home/<USER>/MyProj2025/app/api/routes.py:2883]
2025-06-09 13:43:41,958 INFO: Store name exact match: '龙华店' from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:566]
2025-06-09 13:43:41,958 INFO: 店面认证成功: 店长周鑫芸 -> 店面龙华店 (匹配方式: 完全匹配) [in /home/<USER>/MyProj2025/app/api/routes.py:2955]
2025-06-09 13:43:41,959 INFO: 开始重复订单检查: 手机号=13927441404, 车牌=粤BGW1221, 店面=龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3351]
2025-06-09 13:43:41,996 INFO: 重复订单检查通过: 手机号=13927441404, 车牌=粤BGW1221 [in /home/<USER>/MyProj2025/app/api/routes.py:3582]
2025-06-09 13:43:41,997 INFO: Authentication attempt: manager='周鑫芸', ip=127.0.0.1, user_agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.3 [in /home/<USER>/MyProj2025/app/api/routes.py:695]
2025-06-09 13:43:42,013 INFO: SECURITY: Manager '周鑫芸' authenticated successfully for store '龙华店' (ID: 1) from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:743]
2025-06-09 13:43:42,014 INFO: Creating order for store 龙华店 by manager 周鑫芸 [in /home/<USER>/MyProj2025/app/api/routes.py:3023]
2025-06-09 13:43:42,018 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:191]
2025-06-09 13:43:42,030 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-09 07:43:42.030165+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:88]
2025-06-09 13:43:42,041 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:226]
2025-06-09 13:43:42,082 INFO: F6 order created successfully: 1749447822838 by 周鑫芸 for store 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3144]
2025-06-09 13:43:47,307 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 13:43:47,312 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 13:43:47,359 INFO: 店面 龙华店 活跃订单数量: 4 [in /home/<USER>/MyProj2025/app/api/routes.py:3880]
2025-06-09 13:43:47,364 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3936]
2025-06-09 13:43:49,406 INFO: 原始店面名称参数: '龙华店' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4191]
2025-06-09 13:43:49,408 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-09 13:43:49,408 INFO: 解码后店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4210]
